FROM python:3.12-slim

WORKDIR /application

RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt /application/requirements.txt

RUN pip install --no-cache-dir -r requirements.txt

COPY application /application

# Expose port
EXPOSE 8000

# Use Gunicorn instead of Uvicorn
CMD ["gunicorn", "--config", "gunicorn_config.py", "main:app"]
