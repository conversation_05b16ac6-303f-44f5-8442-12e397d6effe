#!/bin/bash

# Startup script for the OMS application
# Can handle both development (uvicorn) and production (gunicorn) modes

set -e

echo "Starting Rozana OMS Service..."

# Check if we should run in development mode
if [ "$DEVELOPMENT" = "true" ]; then
    echo "Running in development mode with uvicorn..."
    # For development, we can use OpenTelemetry instrument command
    if [ -n "$HYPERDX_API_KEY" ]; then
        echo "Starting with HyperDX instrumentation..."
        opentelemetry-instrument uvicorn app.main:app --host 0.0.0.0 --port ${PORT:-8000} --reload
    else
        echo "Starting without HyperDX (API key not provided)..."
        uvicorn app.main:app --host 0.0.0.0 --port ${PORT:-8000} --reload
    fi
else
    echo "Running in production mode with gunicorn..."
    # For production, use gunicorn with post-fork hook
    gunicorn app.main:app -c gunicorn_config.py
fi
