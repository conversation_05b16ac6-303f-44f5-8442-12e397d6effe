// Rozana Payment Test Client JavaScript
class RozanaPaymentClient {
    constructor() {
        this.currentOrder = null;
        this.razorpayInstance = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.log('🚀 Rozana Payment Test Client initialized');
    }

    setupEventListeners() {
        document.getElementById('orderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createOrder();
        });

        document.getElementById('payButton').addEventListener('click', () => {
            this.initiatePayment();
        });
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logElement = document.getElementById('logs');
        if (logElement) {
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
        }
        console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
    }

    clearLogs() {
        document.getElementById('logs').textContent = '';
    }

    getAuthHeaders() {
        const firebaseToken = document.getElementById('firebaseToken').value;
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (firebaseToken) {
            headers['authorization'] = firebaseToken;
        }
        
        return headers;
    }

    validateAuth() {
        const firebaseToken = document.getElementById('firebaseToken').value;
        if (!firebaseToken) {
            this.log('❌ Firebase ID Token is required for authentication', 'error');
            alert('Please enter your Firebase ID Token in the configuration section');
            return false;
        }
        return true;
    }

    async createOrder() {
        try {
            this.log('📝 Creating order...');
            this.setButtonLoading('Create Order', true);

            // Validate Firebase token
            if (!this.validateAuth()) {
                return;
            }

            const orderData = this.getOrderFormData();
            const apiBaseUrl = document.getElementById('apiBaseUrl').value;
            const headers = this.getAuthHeaders();

            const response = await fetch(`${apiBaseUrl}/app/v1/create_order`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(orderData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            this.log(`✅ Order created successfully: ID ${result.order_id}`);
            this.currentOrder = result;
            
            // Now create payment order
            await this.createPaymentOrder(result.order_id);

        } catch (error) {
            this.log(`❌ Error creating order: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading('Create Order', false);
        }
    }

    async createPaymentOrder(orderId) {
        try {
            this.log('💳 Creating payment order...');
            const apiBaseUrl = document.getElementById('apiBaseUrl').value;
            const headers = this.getAuthHeaders();

            const paymentOrderData = {
                order_id: orderId,
                amount: this.currentOrder.total_amount || parseFloat(document.getElementById('totalAmount').value),
                customer_id: document.getElementById('customerId').value,
                customer_name: document.getElementById('customerName').value,
                customer_email: `${document.getElementById('customerName').value.toLowerCase().replace(' ', '')}@test.com`,
                customer_phone: document.getElementById('phoneNumber').value
            };

            this.log(`💳 Payment order data: ${JSON.stringify(paymentOrderData, null, 2)}`);

            this.log(`🌐 Making request to: ${apiBaseUrl}/app/v1/create_payment_order`);
            this.log(`📋 Headers: ${JSON.stringify(headers, null, 2)}`);

            const response = await fetch(`${apiBaseUrl}/app/v1/create_payment_order`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(paymentOrderData)
            });

            this.log(`📡 Response status: ${response.status}`);
            this.log(`📡 Response headers: ${JSON.stringify([...response.headers.entries()], null, 2)}`);

            if (!response.ok) {
                const errorText = await response.text();
                this.log(`❌ Error response body: ${errorText}`);
                throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
            }

            const paymentOrder = await response.json();
            this.log(`✅ Payment order created: ${paymentOrder.razorpay_order_id}`);
            
            this.currentOrder.payment_order = paymentOrder;
            this.displayOrderDetails();
            this.showPaymentStep();

        } catch (error) {
            this.log(`❌ Error creating payment order: ${error.message}`, 'error');
        }
    }

    getOrderFormData() {
        const items = [];
        const itemCards = document.querySelectorAll('.item-card');
        
        itemCards.forEach(card => {
            const sku = card.querySelector('.sku').value;
            const quantity = parseInt(card.querySelector('.quantity').value);
            const unitPrice = parseFloat(card.querySelector('.unit-price').value);
            const salePrice = parseFloat(card.querySelector('.sale-price').value);
            
            // Only add item if all fields have values
            if (sku && !isNaN(quantity) && !isNaN(unitPrice) && !isNaN(salePrice)) {
                items.push({
                    sku: sku,
                    quantity: quantity,
                    unit_price: unitPrice,
                    sale_price: salePrice
                });
            }
        });

        return {
            customer_id: document.getElementById('customerId').value,
            customer_name: document.getElementById('customerName').value,
            facility_id: document.getElementById('facilityId').value,
            facility_name: document.getElementById('facilityName').value,
            status: "pending",
            total_amount: parseFloat(document.getElementById('totalAmount').value),
            is_approved: false,
            items: items,
            payment: {
                payment_mode: "razorpay",
            },
            address: {
                full_name: document.getElementById('fullName').value,
                phone_number: document.getElementById('phoneNumber').value,
                address_line1: document.getElementById('addressLine1').value,
                address_line2: document.getElementById('addressLine2').value,
                city: document.getElementById('city').value,
                state: document.getElementById('state').value,
                postal_code: document.getElementById('postalCode').value,
                country: document.getElementById('country').value,
                type_of_address: document.getElementById('addressType').value
            }
        };
    }

    displayOrderDetails() {
        const orderDetails = document.getElementById('orderDetails');
        const order = this.currentOrder;
        const paymentOrder = order.payment_order;

        orderDetails.innerHTML = `
            <div class="order-summary-card">
                <div class="summary-header">
                    <i class="fas fa-receipt"></i>
                    <h3>Order Summary</h3>
                </div>
                <div class="summary-details">
                    <div class="detail-row">
                        <span class="label"><i class="fas fa-hashtag"></i> Order ID:</span>
                        <span class="value">${order.order_id}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label"><i class="fas fa-rupee-sign"></i> Amount:</span>
                        <span class="value amount">₹${paymentOrder.amount / 100}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label"><i class="fas fa-globe"></i> Currency:</span>
                        <span class="value">${paymentOrder.currency}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label"><i class="fas fa-barcode"></i> Razorpay Order:</span>
                        <span class="value code">${paymentOrder.razorpay_order_id}</span>
                    </div>
                </div>
            </div>
        `;
    }

    showPaymentStep() {
        this.updateJourneyProgress(2);
        document.getElementById('orderStep').classList.remove('active');
        document.getElementById('paymentStep').classList.add('active');
        document.getElementById('paymentStep').scrollIntoView({ behavior: 'smooth' });
    }

    updateJourneyProgress(activeStep) {
        // Reset all steps
        document.querySelectorAll('.step').forEach(step => {
            step.classList.remove('active', 'completed');
        });
        
        // Mark completed steps
        for (let i = 1; i < activeStep; i++) {
            document.getElementById(`step${i}`).classList.add('completed');
        }
        
        // Mark active step
        document.getElementById(`step${activeStep}`).classList.add('active');
    }

    async initiatePayment() {
        try {
            const razorpayKeyId = document.getElementById('razorpayKeyId').value;
            if (!razorpayKeyId) {
                alert('Please enter your Razorpay Key ID in the configuration section');
                return;
            }

            this.log('🔄 Initiating Razorpay payment...');
            
            const paymentOrder = this.currentOrder.payment_order;
            const options = {
                key: razorpayKeyId,
                amount: paymentOrder.amount,
                currency: paymentOrder.currency,
                name: 'Rozana',
                description: `Payment for Order #${this.currentOrder.order_id}`,
                order_id: paymentOrder.razorpay_order_id,
                handler: (response) => {
                    this.handlePaymentSuccess(response);
                },
                prefill: {
                    name: 'Test Customer',
                    email: '<EMAIL>',
                    contact: document.getElementById('phoneNumber').value
                },
                theme: {
                    color: '#667eea'
                },
                modal: {
                    ondismiss: () => {
                        this.log('⚠️ Payment cancelled by user');
                    }
                }
            };

            this.razorpayInstance = new Razorpay(options);
            this.razorpayInstance.open();

        } catch (error) {
            this.log(`❌ Error initiating payment: ${error.message}`, 'error');
        }
    }

    async handlePaymentSuccess(response) {
        try {
            this.log('🎉 Payment completed! Verifying...');
            this.log(`Payment ID: ${response.razorpay_payment_id}`);
            this.log(`Order ID: ${response.razorpay_order_id}`);
            this.log(`Signature: ${response.razorpay_signature}`);

            const apiBaseUrl = document.getElementById('apiBaseUrl').value;
            const headers = this.getAuthHeaders();
            
            const verificationResponse = await fetch(`${apiBaseUrl}/app/v1/verify_payment`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    oms_order_id: this.currentOrder.order_id,
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_signature: response.razorpay_signature
                })
            });

            if (!verificationResponse.ok) {
                throw new Error(`Verification failed! status: ${verificationResponse.status}`);
            }

            const verificationResult = await verificationResponse.json();
            this.log('✅ Payment verified successfully!');
            this.log(`Verification result: ${JSON.stringify(verificationResult, null, 2)}`);
            
            this.showCompletionStep();
            await this.checkPaymentStatus();

        } catch (error) {
            this.log(`❌ Payment verification failed: ${error.message}`, 'error');
            this.showPaymentStatus('error');
        }
    }

    async checkPaymentStatus() {
        try {
            this.log('🔍 Checking payment status...');
            const apiBaseUrl = document.getElementById('apiBaseUrl').value;
            const headers = this.getAuthHeaders();
            
            const response = await fetch(`${apiBaseUrl}/app/v1/payment_status/${this.currentOrder.order_id}`, {
                method: 'GET',
                headers: headers
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const statusResult = await response.json();
            this.log(`📊 Payment status: ${JSON.stringify(statusResult, null, 2)}`);
            
            this.displayPaymentStatus(statusResult);

        } catch (error) {
            this.log(`❌ Error checking payment status: ${error.message}`, 'error');
        }
    }

    showCompletionStep() {
        this.updateJourneyProgress(3);
        document.getElementById('paymentStep').classList.remove('active');
        document.getElementById('completionStep').classList.add('active');
        document.getElementById('completionStep').scrollIntoView({ behavior: 'smooth' });
    }

    displayPaymentStatus(statusData) {
        const statusElement = document.getElementById('paymentStatus');
        
        if (!statusData.success) {
            statusElement.innerHTML = `
                <div class="status-error">
                    <h3>❌ Error Loading Payment Status</h3>
                    <p>Unable to fetch payment information</p>
                </div>
            `;
            return;
        }

        const paymentSummary = statusData.payment_summary;
        let statusClass = 'status-pending';
        let statusIcon = '⏳';
        let statusText = 'Unknown';
        
        // Determine overall payment status
        if (paymentSummary.has_payments) {
            if (paymentSummary.completed_count > 0 && paymentSummary.pending_count === 0 && paymentSummary.failed_count === 0) {
                statusClass = 'status-success';
                statusIcon = '✅';
                statusText = 'Payment Completed';
            } else if (paymentSummary.pending_count > 0) {
                statusClass = 'status-pending';
                statusIcon = '⏳';
                statusText = 'Payment Pending';
            } else if (paymentSummary.failed_count > 0) {
                statusClass = 'status-error';
                statusIcon = '❌';
                statusText = 'Payment Failed';
            }
        } else {
            statusClass = 'status-pending';
            statusIcon = '⏳';
            statusText = 'No Payments Found';
        }

        // Build payment details HTML
        let paymentsHtml = '';
        if (paymentSummary.payments && paymentSummary.payments.length > 0) {
            paymentsHtml = paymentSummary.payments.map(payment => {
                const paymentStatusClass = payment.payment_status_display === 'Payment Completed' ? 'payment-success' : 'payment-pending';
                const paymentIcon = payment.payment_status_display === 'Payment Completed' ? '✅' : '⏳';
                
                return `
                    <div class="payment-item ${paymentStatusClass}">
                        <div class="payment-header">
                            <span class="payment-icon">${paymentIcon}</span>
                            <span class="payment-status">${payment.payment_status_display}</span>
                            <span class="payment-amount">₹${payment.payment_amount}</span>
                        </div>
                        <div class="payment-details">
                            <p><strong>Payment ID:</strong> ${payment.payment_id}</p>
                            <p><strong>Mode:</strong> ${payment.payment_mode.toUpperCase()}</p>
                            <p><strong>Cash Amount:</strong> ₹${payment.cash_amount}</p>
                            <p><strong>Online Amount:</strong> ₹${payment.online_amount}</p>
                            <p><strong>Created:</strong> ${new Date(payment.created_at).toLocaleString()}</p>
                            <p><strong>Updated:</strong> ${new Date(payment.updated_at).toLocaleString()}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        statusElement.innerHTML = `
            <div class="${statusClass}">
                <div class="status-header">
                    <span class="status-icon">${statusIcon}</span>
                    <h3>${statusText}</h3>
                </div>
                <div class="status-summary">
                    <p><strong>Order ID:</strong> ${statusData.order_id}</p>
                    <p><strong>Order Status:</strong> ${statusData.order_status}</p>
                    <p><strong>Total Paid:</strong> ₹${paymentSummary.total_paid}</p>
                    <p><strong>Payment Count:</strong> ${paymentSummary.payment_count}</p>
                    <div class="payment-counts">
                        <span class="count-item completed">✅ Completed: ${paymentSummary.completed_count}</span>
                        <span class="count-item pending">⏳ Pending: ${paymentSummary.pending_count}</span>
                        <span class="count-item failed">❌ Failed: ${paymentSummary.failed_count}</span>
                    </div>
                </div>
                ${paymentsHtml ? `<div class="payments-list">${paymentsHtml}</div>` : ''}
            </div>
        `;
    }

    setButtonLoading(buttonText, isLoading) {
        const submitButton = document.querySelector('button[type="submit"]');
        if (isLoading) {
            submitButton.innerHTML = `<span class="loading"></span>${buttonText}...`;
            submitButton.disabled = true;
        } else {
            submitButton.innerHTML = buttonText;
            submitButton.disabled = false;
        }
    }
}

// Utility functions for managing order items
function addItem() {
    const container = document.getElementById('orderItems');
    const itemCount = container.children.length + 1;
    const newItem = document.createElement('div');
    newItem.className = 'item-card';
    newItem.innerHTML = `
        <div class="item-header">
            <i class="fas fa-cube"></i>
            <span>Item #${itemCount}</span>
            <button type="button" class="remove-item" onclick="removeItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="item-fields">
            <div class="form-group">
                <label><i class="fas fa-barcode"></i> SKU:</label>
                <input type="text" placeholder="SKU-000${itemCount}" class="sku" required>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-sort-numeric-up"></i> Quantity:</label>
                    <input type="number" placeholder="1" value="1" class="quantity" required>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-tag"></i> Unit Price:</label>
                    <input type="number" placeholder="0" step="0.01" class="unit-price" required>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-money-bill"></i> Sale Price:</label>
                    <input type="number" placeholder="0" step="0.01" class="sale-price" required>
                </div>
            </div>
        </div>
    `;
    container.appendChild(newItem);
}

function removeItem(button) {
    const itemRow = button.parentElement;
    const container = document.getElementById('orderItems');
    if (container.children.length > 1) {
        itemRow.remove();
    } else {
        alert('At least one item is required');
    }
}

function addLog(message, type = 'info') {
    const logsElement = document.getElementById('logs');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
    logsElement.textContent += logEntry;
    logsElement.scrollTop = logsElement.scrollHeight;
}

function clearLogs() {
    document.getElementById('logs').textContent = '';
    addLog('Debug logs cleared', 'info');
}

function checkPaymentStatus() {
    if (window.paymentClient && window.paymentClient.currentOrder) {
        window.paymentClient.checkPaymentStatus();
    } else {
        alert('No active order found. Please create an order first.');
    }
}

// Settings modal functions
function openSettings() {
    document.getElementById('settingsModal').style.display = 'block';
}

function closeSettings() {
    document.getElementById('settingsModal').style.display = 'none';
}

function saveSettings() {
    // Save settings to localStorage
    localStorage.setItem('apiBaseUrl', document.getElementById('apiBaseUrl').value);
    localStorage.setItem('razorpayKeyId', document.getElementById('razorpayKeyId').value);
    localStorage.setItem('firebaseToken', document.getElementById('firebaseToken').value);
    
    closeSettings();
    
    // Show success message
    if (window.paymentClient) {
        window.paymentClient.log('✅ Configuration saved successfully!');
    }
}

function loadSettings() {
    // Load settings from localStorage
    const apiBaseUrl = localStorage.getItem('apiBaseUrl');
    const razorpayKeyId = localStorage.getItem('razorpayKeyId');
    const firebaseToken = localStorage.getItem('firebaseToken');
    
    if (apiBaseUrl) document.getElementById('apiBaseUrl').value = apiBaseUrl;
    if (razorpayKeyId) document.getElementById('razorpayKeyId').value = razorpayKeyId;
    if (firebaseToken) document.getElementById('firebaseToken').value = firebaseToken;
}

function toggleLogs() {
    const logsPanel = document.querySelector('.logs-panel');
    const toggleIcon = document.getElementById('toggleIcon');
    
    logsPanel.classList.toggle('collapsed');
    
    if (logsPanel.classList.contains('collapsed')) {
        toggleIcon.classList.remove('fa-chevron-right');
        toggleIcon.classList.add('fa-chevron-down');
    } else {
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-right');
    }
}

function resetJourney() {
    // Reset to step 1
    document.querySelectorAll('.journey-step').forEach(step => {
        step.classList.remove('active');
    });
    document.getElementById('orderStep').classList.add('active');
    
    // Reset progress
    if (window.paymentClient) {
        window.paymentClient.updateJourneyProgress(1);
        window.paymentClient.currentOrder = null;
    }
    
    // Clear form
    document.getElementById('orderForm').reset();
    
    // Scroll to top
    document.getElementById('orderStep').scrollIntoView({ behavior: 'smooth' });
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('settingsModal');
    if (event.target === modal) {
        closeSettings();
    }
}

// Initialize the payment client when the page loads
document.addEventListener('DOMContentLoaded', () => {
    loadSettings();
    window.paymentClient = new RozanaPaymentClient();
    
    // Add initial log to show debug panel is working
    if (window.paymentClient) {
        window.paymentClient.log('🚀 Rozana Payment Test Client initialized', 'info');
        window.paymentClient.log('📋 Debug logging is active', 'info');
        window.paymentClient.log('⚙️ Configure your API settings using the gear icon', 'info');
    }
    
    // Ensure logs panel is visible by default (as requested)
    const logsPanel = document.querySelector('.logs-panel');
    const toggleIcon = document.getElementById('toggleIcon');
    if (logsPanel && toggleIcon) {
        logsPanel.classList.remove('collapsed');
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-right');
    }
});
