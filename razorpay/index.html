<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Test Client</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body>
    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-cog"></i> Configuration</h2>
                <span class="close" onclick="closeSettings()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="apiBaseUrl"><i class="fas fa-server"></i>API Base URL</label>
                    <input type="url" id="apiBaseUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
                </div>
                <div class="form-group">
                    <label for="razorpayKeyId"><i class="fas fa-key"></i>Razorpay Key ID</label>
                    <input type="text" id="razorpayKeyId" placeholder="Enter your Razorpay Key ID">
                </div>
                <div class="form-group">
                    <label for="firebaseToken"><i class="fas fa-shield-alt"></i>Firebase ID Token</label>
                    <input type="password" id="firebaseToken" placeholder="Enter your Firebase ID Token">
                    <small>Required for authentication. Get this from your Firebase app.</small>
                </div>
                <button onclick="saveSettings()" class="save-btn"><i class="fas fa-save"></i> Save Configuration</button>
            </div>
        </div>
    </div>

    <div class="main-layout">
        <!-- Main Content Area (70%) -->
        <div class="content-area">
            <div class="app-container">
                <!-- Header with Settings -->
                <header class="app-header">
                    <div class="header-content">
                        <div class="logo">
                            <i class="fas fa-shopping-cart"></i>
                            <h1>Payment Test</h1>
                        </div>
                        <button class="settings-btn" onclick="openSettings()">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <p class="subtitle">Test your Razorpay integration with a beautiful journey</p>
                </header>

        <!-- Journey Progress -->
        <div class="journey-progress">
            <div class="step active" id="step1">
                <div class="step-circle">1</div>
                <span>Create Order</span>
            </div>
            <div class="step-line"></div>
            <div class="step" id="step2">
                <div class="step-circle">2</div>
                <span>Payment</span>
            </div>
            <div class="step-line"></div>
            <div class="step" id="step3">
                <div class="step-circle">3</div>
                <span>Complete</span>
            </div>
        </div>

        <!-- Step 1: Create Order -->
        <div class="journey-step active" id="orderStep">
            <div class="glass-card">
                <div class="card-header">
                    <i class="fas fa-shopping-bag"></i>
                    <h2>Create Your Order</h2>
                    <p>Fill in the order details to get started</p>
                </div>
                <form id="orderForm" class="order-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerId"><i class="fas fa-user"></i>Customer ID</label>
                            <input type="text" id="customerId" value="2CN3aYJnaGXpaguuctWAubZnKKp1" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="customerName"><i class="fas fa-user-tag"></i>Customer Name</label>
                            <input type="text" id="customerName" value="Test Customer" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="facilityId"><i class="fas fa-warehouse"></i>Facility ID</label>
                            <input type="text" id="facilityId" value="1" required>
                        </div>

                        <div class="form-group">
                            <label for="facilityName"><i class="fas fa-building"></i>Facility Name</label>
                            <input type="text" id="facilityName" value="WH-1" required>
                        </div>
                    </div>

                    <div class="form-group amount-group">
                        <label for="totalAmount"><i class="fas fa-rupee-sign"></i>Total Amount</label>
                        <input type="number" id="totalAmount" value="781" step="0.01" required>
                    </div>

                    <div class="section-header">
                        <i class="fas fa-map-marker-alt"></i>
                        <h3>Delivery Address</h3>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fullName"><i class="fas fa-user"></i>Full Name</label>
                            <input type="text" id="fullName" value="Test Customer" required>
                        </div>

                        <div class="form-group">
                            <label for="phoneNumber"><i class="fas fa-phone"></i>Phone Number</label>
                            <input type="tel" id="phoneNumber" value="9123456789" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="addressLine1"><i class="fas fa-home"></i>Address Line 1</label>
                        <input type="text" id="addressLine1" value="123 Main Street" required>
                    </div>

                    <div class="form-group">
                        <label for="addressLine2"><i class="fas fa-building"></i>Address Line 2</label>
                        <input type="text" id="addressLine2" value="Apt 4B">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="city"><i class="fas fa-city"></i>City</label>
                            <input type="text" id="city" value="Bangalore" required>
                        </div>

                        <div class="form-group">
                            <label for="state"><i class="fas fa-map"></i>State</label>
                            <input type="text" id="state" value="Karnataka" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="postalCode"><i class="fas fa-mail-bulk"></i>Postal Code</label>
                            <input type="text" id="postalCode" value="56001" required>
                        </div>

                        <div class="form-group">
                            <label for="country"><i class="fas fa-globe"></i>Country</label>
                            <input type="text" id="country" value="INDIA" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="addressType"><i class="fas fa-tag"></i> Address Type:</label>
                        <select id="addressType" required>
                            <option value="home">🏠 Home</option>
                            <option value="work" selected>🏢 Work</option>
                            <option value="other">📍 Other</option>
                        </select>
                    </div>

                    <div class="section-header">
                        <i class="fas fa-box"></i>
                        <h3>Order Items</h3>
                    </div>
                    
                    <div class="items-container">
                        <div id="orderItems">
                            <div class="item-card">
                                <div class="item-header">
                                    <i class="fas fa-cube"></i>
                                    <span>Item #1</span>
                                    <button type="button" class="remove-item" onclick="removeItem(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <div class="item-fields">
                                    <div class="form-group">
                                        <label><i class="fas fa-barcode"></i> SKU:</label>
                                        <input type="text" placeholder="SKU-0003" value="SKU-0003" class="sku" required>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label><i class="fas fa-sort-numeric-up"></i> Quantity:</label>
                                            <input type="number" placeholder="1" value="1" class="quantity" required>
                                        </div>
                                        <div class="form-group">
                                            <label><i class="fas fa-tag"></i> Unit Price:</label>
                                            <input type="number" placeholder="781" value="781" step="0.01" class="unit-price" required>
                                        </div>
                                        <div class="form-group">
                                            <label><i class="fas fa-money-bill"></i> Sale Price:</label>
                                            <input type="number" placeholder="781" value="781" step="0.01" class="sale-price" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" class="add-item-btn" onclick="addItem()">
                            <i class="fas fa-plus"></i> Add Another Item
                        </button>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="journey-btn">
                            <i class="fas fa-arrow-right"></i>
                            Create Order
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Step 2: Payment -->
        <div class="journey-step" id="paymentStep">
            <div class="glass-card">
                <div class="card-header">
                    <i class="fas fa-credit-card"></i>
                    <h2>Complete Payment</h2>
                    <p>Review your order and proceed to payment</p>
                </div>
                <div id="orderDetails" class="order-summary"></div>
                <div class="form-actions">
                    <button id="payButton" class="journey-btn payment-btn">
                        <i class="fas fa-lock"></i>
                        Pay Securely
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 3: Completion -->
        <div class="journey-step" id="completionStep">
            <div class="glass-card">
                <div class="card-header success">
                    <i class="fas fa-check-circle"></i>
                    <h2>Payment Complete!</h2>
                    <p>Your order has been processed successfully</p>
                </div>
                <div id="paymentStatus" class="status-details"></div>
                <div class="form-actions">
                    <button onclick="checkPaymentStatus()" class="journey-btn secondary">
                        <i class="fas fa-sync-alt"></i>
                        Refresh Status
                    </button>
                    <button onclick="resetJourney()" class="journey-btn">
                        <i class="fas fa-plus"></i>
                        New Order
                    </button>
                </div>
            </div>
        </div>
            </div>
        </div>

        <!-- Debug Logs Panel (30%) -->
        <div class="logs-panel">
            <div class="logs-header" onclick="toggleLogs()">
                <i class="fas fa-terminal"></i>
                <h3>Debug Logs</h3>
                <i class="fas fa-chevron-right toggle-icon" id="toggleIcon"></i>
            </div>
            <div class="logs-content" id="logsContent">
                <div id="logs" class="logs-display"></div>
                <div class="logs-actions">
                    <button onclick="clearLogs()" class="logs-btn">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
