from app.dto.orders import OrderCreate


class OrderCreateValidator:
    def __init__(self, order: OrderCreate = None, user_id: str = None):
        self.order = order
        self.user_id = user_id

    def validate(self):
        self.validate_user_id_customer_id()
        self.validate_duplicate_sku_items()

    def validate_user_id_customer_id(self):
        if self.user_id != self.order.customer_id:
            raise ValueError("User ID and Customer ID do not match")

    def validate_page_size(self, limit: int = 20, offset: int = 0):
        # max difference between limit and offset should be 20
        if abs(limit - offset) > 20:
            raise ValueError("Pagination size should be less than 20")

    def validate_items_count(self):
        """Validate that the order has at least one item."""
        if not self.order.items or len(self.order.items) < 1:
            raise HTTPException(status_code=400, detail="Order must contain at least one item")

    def validate_duplicate_sku_items(self):
        sku_list = [item.sku for item in self.order.items]
        duplicates = [sku for sku in sku_list if sku_list.count(sku) > 1]
        if duplicates:
            raise ValueError(f"Duplicate SKU items found: {', '.join(set(duplicates))}")

    def validate_payment_mode_by_origin(self, origin: str):
        """Validate payment mode based on origin (app/pos)"""
        # Payment object is now mandatory, so we can directly access it
        payment_mode = self.order.payment.payment_mode.lower()

        if origin == "app":
            # App origin: only 'cod' and 'razorpay' are allowed
            allowed_modes = {"cod", "razorpay"}
            if payment_mode not in allowed_modes:
                raise ValueError(f"For app origin, payment_mode must be one of: {', '.join(allowed_modes)}. Got: {payment_mode}")

        elif origin == "pos":
            # POS origin: 'cash', 'cash_and_online', and 'razorpay' are allowed
            allowed_modes = {"cash", "cash_and_online", "razorpay"}
            if payment_mode not in allowed_modes:
                raise ValueError(f"For pos origin, payment_mode must be one of: {', '.join(allowed_modes)}. Got: {payment_mode}")

        # For other origins, no specific restrictions (or add as needed)

    def validate_create_payment_order(self, origin: str):
        """Validate create_payment_order field based on origin and payment method"""
        if not self.order.payment.create_payment_order:
            # If create_payment_order is False, no validation needed
            return

        # create_payment_order is only valid for 'app' or 'pos' origins
        if origin not in ["app", "pos"]:
            raise ValueError(f"create_payment_order is only valid for 'app' or 'pos' origins. Got origin: {origin}")

        # Payment object is now mandatory, so we can directly access it
        payment_mode = self.order.payment.payment_mode.lower()
        # create_payment_order is only valid for 'razorpay' or 'cash_and_online'
        valid_payment_modes = {"razorpay", "cash_and_online"}
        if payment_mode not in valid_payment_modes:
            raise ValueError(f"create_payment_order is only valid for payment modes: {', '.join(valid_payment_modes)}. Got: {payment_mode}")

    def validate_payment_configuration(self, origin: str):
        """Comprehensive validation for payment configuration based on origin"""
        self.validate_payment_mode_by_origin(origin)
        self.validate_create_payment_order(origin)

