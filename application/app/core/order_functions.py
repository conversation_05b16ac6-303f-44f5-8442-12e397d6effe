import os
import logging
from fastapi import Request, HTTPException, BackgroundTasks
from app.connections.database import get_db, get_read_db
from app.services.order_service import OrderService
from app.services.order_query_service import OrderQueryService
from app.dto.orders import OrderCreate  # type: ignore
from app.validations.orders import OrderCreateValidator
from app.validations.stock import StockValidator
from app.core.constants import OrderStatus, PaymentStatus
from app.integrations.wms_service import wms_service
from app.services.order_service import OrderService

STOCK_CHECK_ENABLED = os.getenv("STOCK_CHECK_ENABLED", "false").lower() == "true"

logger = logging.getLogger(__name__)


async def create_order_core(order: OrderCreate, request: Request, background_tasks: BackgroundTasks, origin: str = "app"):
    """Shared logic for creating an order and its items."""
    try:
        # Validation Layer
        user_id = getattr(request.state, "user_id", None)
        validator = OrderCreateValidator(order, user_id)

        if origin == "app":
            validator.validate_user_id_customer_id()
            if STOCK_CHECK_ENABLED:
                for item in order.items:
                    stock_validator = StockValidator(order.facility_name, item.sku)
                    stock_validator.validate_stock()

        validator.validate_items_count()
        validator.validate_duplicate_sku_items()
        validator.validate_payment_configuration(origin)
        logger.info("Create order requested by user_id=%s, origin=%s", user_id, origin)

        # Service Layer
        service = OrderService()
        order_data = order.model_dump()
        result = await service.create_order(order_data, background_tasks, origin)

        if not result.get("success", False):
            raise HTTPException(
                status_code=400, detail=result.get("message", "Failed to create order")
            )

        #Block the stock in redis by reduciing the avaialable quantity
        if STOCK_CHECK_ENABLED and origin == "app":
            for item in order.items:
                stock_validator = StockValidator(order.facility_name, item.sku)
                stock_validator.block_stock(item.quantity)

        # Schedule WMS sync conditionally based on payment mode
        # For COD/cash orders: sync immediately after order creation
        # For online/razorpay payments: sync only after payment completion
        payment_mode = order.payment.payment_mode.lower()
        is_cod_order = payment_mode in ["cash", "cod"]
        if background_tasks and result.get("order_id") and is_cod_order:
            # For COD/cash orders, sync to WMS immediately
            background_tasks.add_task(
                wms_service.sync_order_by_id,
                order.facility_name,
                result["order_id"],
                service
            )
            logger.info(f"Scheduled immediate WMS sync for {payment_mode.upper()} order {result['order_id']}")
        else:
            # For online/razorpay payment orders, WMS sync will be triggered after payment completion
            logger.info(f"Skipping immediate WMS sync for {payment_mode.upper()} payment order {result['order_id']} - will sync after payment completion")

        # Auto-create payment order if requested and not a COD order
        if order.payment.create_payment_order and not is_cod_order:
            try:
                payment_order_details = await create_payment_order_for_existing_order(
                    order_id=result["order_id"],
                    customer_id=order.customer_id,
                    customer_name=order.customer_name,
                    customer_email=order.customer_email,
                    customer_phone=order.customer_phone,
                    amount=order.total_amount
                )
                result["payment_order_details"] = payment_order_details
                logger.info("Payment order created automatically for order %s", result["order_id"])
            except Exception as payment_exc:
                logger.error("Failed to create payment order for %s: %s", result["order_id"], payment_exc)
                # Don't fail the entire order creation, just log the error
                result["payment_order_details"] = {
                    "error": "Failed to create payment order",
                    "message": str(payment_exc)
                }

        return result
    except HTTPException:
        raise
    except ValueError as exc:
        raise HTTPException(status_code=400, detail=str(exc)) from exc
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Error creating order: %s", exc)
        raise HTTPException(status_code=500, detail="Internal server error") from exc


async def get_order_details_core(order_id: str):
    """Shared logic for fetching full order details."""
    try:
        service = OrderQueryService()
        order = service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")
        return order
    except HTTPException:
        raise
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Error getting order details %s: %s", order_id, exc)
        raise HTTPException(status_code=500, detail="Internal server error") from exc


async def get_all_orders_core(request: Request, limit: int, offset: int, sort_order: str):
    """Shared logic for listing all orders for the authenticated user."""
    user_id = getattr(request.state, "user_id", None)
    try:
        service = OrderQueryService()
        # Validate page parameters
        validator = OrderCreateValidator(user_id=user_id)
        validator.validate_page_size(limit, offset)

        orders = service.get_all_orders(user_id, limit, offset, sort_order)
        if not orders:
            raise HTTPException(status_code=404, detail="No orders found")
        return orders
    except HTTPException:
        raise
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Error getting all orders for user %s: %s", user_id, exc)
        raise HTTPException(status_code=500, detail="Internal server error") from exc

async def get_all_facility_orders_core(request: Request, facility_name: str, limit: int, offset: int, sort_order: str):
    """Shared logic for listing all orders for a facility."""
    try:
        service = OrderQueryService()
        # Validate page parameters
        validator = OrderCreateValidator()
        validator.validate_page_size(limit, offset)

        orders = service.get_all_facility_orders(facility_name, limit, offset, sort_order)
        if not orders:
            raise HTTPException(status_code=404, detail="No orders found")
        return orders
    except HTTPException:
        raise
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Error getting all orders for facility %s: %s", facility_name, exc)
        raise HTTPException(status_code=500, detail="Internal server error") from exc


async def create_payment_order_for_existing_order(
    order_id: str,
    customer_id: str,
    customer_name: str,
    customer_email: str = None,
    customer_phone: str = None,
    amount: float = None
):
    """Create a Razorpay payment order for an existing OMS order using existing service."""
    from app.integrations.razorpay_service import razorpay_service
    
    try:
        # Get the existing order to extract amount if not provided
        order = await get_order_by_id(order_id)
        if not order:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found")
        
        # Use provided amount or fall back to order's total_amount
        payment_amount = amount or order.get("total_amount")
        if not payment_amount:
            raise ValueError("Payment amount not available")
        
        # Prepare customer details with defaults
        customer_details = {
            "customer_id": customer_id,
            "customer_name": customer_name,
            "customer_email": customer_email or f"{customer_id}@gmail.com",
            "customer_phone": customer_phone or "9999999999"
        }

        # Reuse existing Razorpay service method
        result = await razorpay_service.create_razorpay_order(
            order_id=order_id,
            amount=payment_amount,
            customer_details=customer_details,
            notes={
                "oms_order_id": order_id,
                "customer_id": customer_id,
                "customer_name": customer_name
            }
        )
        
        if result["success"]:
            return {
                "razorpay_order_id": result["razorpay_order_id"],
                "amount": result["amount"],
                "amount_paise": result["amount_paise"],
                "currency": result["currency"],
                "key_id": result["key_id"],
                "customer_details": customer_details,
                "created_at": result["created_at"]
            }
        else:
            if result.get("skipped"):
                raise ValueError("Razorpay integration is disabled")
            else:
                raise ValueError(result.get("message", "Failed to create payment order"))
        
    except Exception as exc:
        logger.error("Error creating payment order for %s: %s", order_id, exc)
        raise exc


async def get_order_by_id(order_id: str):
    """Get order details by order ID for payment operations."""
    try:
        service = OrderQueryService()
        result = service.get_order_by_id(order_id)
        
        if not result:
            return None
            
        return result
        
    except Exception as e:
        logger.error(f"Error fetching order {order_id}: {e}")
        return None


async def create_payment_for_order(
    order_id: str,
    payment_id: str,
    payment_amount: float,
    payment_mode: str = "online",
    cash_amount: float = 0.0,
    online_amount: float = None
):
    """Create a payment record for an order (payments table only)."""
    try:
        from app.services.payment_service import payment_service
        from decimal import Decimal
        
        # First, get the internal integer ID from the external string order_id
        order = await get_order_by_id(order_id)
        if not order:
            logger.error(f"Order not found for order_id: {order_id}")
            return {"success": False, "message": f"Order {order_id} not found"}
        
        internal_order_id = order.get("id")  # Get the internal integer ID
        
        # Set online_amount to payment_amount if not provided
        if online_amount is None:
            online_amount = payment_amount
        
        result = await payment_service.create_or_update_payment_record(
            order_id=internal_order_id,  # Use internal integer ID
            payment_id=payment_id,
            payment_amount=Decimal(str(payment_amount)),
            payment_mode=payment_mode,
            cash_amount=Decimal(str(cash_amount)),
            online_amount=Decimal(str(online_amount)),
            total_amount=Decimal(str(payment_amount))
        )
        
        if result.get("success"):
            logger.info(f"Created payment record for order {order_id}, payment {payment_id}")
        else:
            logger.error(f"Failed to create payment record for order {order_id}: {result.get('message')}")
            
        return result
        
    except Exception as e:
        logger.error(f"Error creating payment for order {order_id}: {e}")
        return {"success": False, "message": str(e)}


async def update_payment_status(
    payment_id: str,
    new_status: int,
    background_tasks: BackgroundTasks = None
):
    """Update payment status and trigger WMS sync if payment is completed for online orders."""
    try:
        from app.services.payment_service import payment_service

        # Get payment details before updating to check if it's an online payment
        payment_details = await payment_service.get_payment_by_id(payment_id)

        result = await payment_service.update_payment_status(
            payment_id=payment_id,
            new_status=new_status
        )

        if result.get("success"):
            logger.info(f"Updated payment {payment_id} status to {new_status}")

            # Trigger WMS sync if payment is completed and it's an online payment
            if (new_status == PaymentStatus.COMPLETED and
                payment_details and
                payment_details.payment_mode in ["online", "cash_and_online"]):

                # Get the external order ID from the order relationship
                if payment_details.order and payment_details.order.order_id:
                    external_order_id = payment_details.order.order_id
                    facility_name = payment_details.order.facility_name
                    
                    if facility_name:
                        # Create OrderService instance for WMS sync
                        order_service = OrderService()

                        # Schedule WMS sync as background task
                        if background_tasks:
                            background_tasks.add_task(
                                wms_service.sync_order_by_id,
                                facility_name,
                                external_order_id,
                                order_service
                            )
                        else:
                            await wms_service.sync_order_by_id(
                                facility_name,
                                external_order_id,
                                order_service
                            )
                        logger.info(f"Scheduled WMS sync for order {external_order_id} after payment completion")
                    else:
                        logger.error(f"Could not find facility_name for order {external_order_id}")
                else:
                    logger.error(f"No order found in payment details for payment {payment_id}")
        else:
            logger.error(f"Failed to update payment {payment_id} status: {result.get('message')}")

        return result
    except Exception as e:
        logger.error(f"Error updating payment status for {payment_id}: {e}")
        return {"success": False, "message": str(e)}


async def get_payment_status_for_order(order_id: str):
    """Get payment status summary for an order (from payments table only)."""
    try:
        from app.services.payment_service import payment_service

        # First, get the internal integer ID from the external string order_id
        order = await get_order_by_id(order_id)
        if not order:
            logger.error(f"Order not found for order_id: {order_id}")
            return {"success": False, "message": f"Order {order_id} not found"}

        internal_order_id = order.get("id")  # Get the internal integer ID

        result = await payment_service.get_payment_status_for_order(internal_order_id)

        logger.info(f"Retrieved payment status for order {order_id}")
        return result

    except Exception as e:
        logger.error(f"Error getting payment status for order {order_id}: {e}")
        return {
            "order_id": order_id,
            "has_payments": False,
            "payment_status": None,
            "total_paid": 0.0,
            "payment_count": 0,
            "error": str(e)
        }

