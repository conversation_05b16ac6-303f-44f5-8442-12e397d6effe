import logging
from fastapi import HTTPException
from app.connections.database import get_raw_transaction
from app.integrations.wms_service import wms_service
from app.core.constants import OrderStatus
from sqlalchemy import text

logger = logging.getLogger(__name__)


async def cancel_order_core(order_id: str):
    try:
        with get_raw_transaction() as conn:
            check_order_sql = """
                SELECT id, order_id, status, facility_name 
                FROM orders 
                WHERE order_id = :order_id
            """
            
            result = conn.execute(text(check_order_sql), {'order_id': order_id})
            order_row = result.fetchone()
            
            if not order_row:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Order {order_id} not found"
                )
            
            current_status = order_row.status
            facility_name = order_row.facility_name
            
            # Define allowed statuses for cancellation
            allowed_statuses = [OrderStatus.OPEN, OrderStatus.WMS_SYNCED, OrderStatus.WMS_SYNC_FAILED, 
                              OrderStatus.WMS_OPEN, OrderStatus.WMS_INPROGRESS]
            
            if current_status not in allowed_statuses:
                raise HTTPException(
                    status_code=400,
                    detail=f"Order {order_id} cannot be cancelled. Current status: {current_status} is not in allowed statuses {allowed_statuses}"
                )
            
            if current_status == OrderStatus.CANCELED:
                return {
                    "success": True,
                    "message": f"Order {order_id} is already cancelled",
                    "order_id": order_id,
                    "status": OrderStatus.CANCELED
                }
            
            update_order_sql = """
                UPDATE orders 
                SET status = :status, updated_at = NOW()
                WHERE order_id = :order_id
            """
            
            conn.execute(text(update_order_sql), {
                'status': OrderStatus.CANCELED,
                'order_id': order_id
            })
            
            update_items_sql = """
                UPDATE order_items 
                SET status = :status, updated_at = NOW()
                WHERE order_id = :order_pk
            """
            
            conn.execute(text(update_items_sql), {
                'status': OrderStatus.CANCELED,
                'order_pk': order_row.id
            })
            
            conn.commit()
            logger.info(f"Order {order_id} status updated to CANCELED in database")
        
        try:
            wms_result = await wms_service.cancel_outbound_order(
                order_reference=order_id,
                warehouse=facility_name
            )
            
            if wms_result.get('success', False):
                logger.info(f"Order {order_id} successfully cancelled in WMS")
                wms_message = "Order cancelled in WMS and stock updated"
            else:
                logger.warning(f"WMS cancellation failed for order {order_id}: {wms_result.get('message', 'Unknown error')}")
                wms_message = f"Order cancelled in OMS but WMS cancellation failed: {wms_result.get('message', 'Unknown error')}"
                
        except Exception as wms_error:
            logger.error(f"WMS cancellation error for order {order_id}: {str(wms_error)}")
            wms_message = f"Order cancelled in OMS but WMS cancellation failed: {str(wms_error)}"
        
        return {
            "success": True,
            "message": f"Order {order_id} cancelled successfully",
            "order_id": order_id,
            "status": OrderStatus.CANCELED,
            "wms_status": wms_message
        }
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error cancelling order {order_id}: {exc}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error while cancelling order: {str(exc)}"
        ) from exc
