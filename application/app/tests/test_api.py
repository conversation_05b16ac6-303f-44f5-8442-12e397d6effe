#!/usr/bin/env python3
"""
Simple test script to verify the OMS API functionality
"""

import requests
import json
from datetime import datetime

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 200

def test_create_order():
    """Test creating a new order"""
    print("\n🔍 Testing order creation...")
    
    order_data = {
        "customer_id": "CUST-001",
        "customer_name": "nithin",
        "facility_id": "FAC-001", 
        "facility_name": "abc",
        "status": "pending",
        "total_amount": 99.99,
        "items": [
            {
                "sku": "ITEM-001",
                "quantity": 2,
                "unit_price": 45.00,
                "sale_price": 49.99
            },
            {
                "sku": "ITEM-002", 
                "quantity": 1,
                "unit_price": 25.00,
                "sale_price": 29.99
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/create_order", json=order_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 200:
        return response.json().get("order_id")
    return None

def test_get_order_details(order_id):
    """Test getting order details"""
    print(f"\n🔍 Testing get order details for {order_id}...")
    
    response = requests.get(f"{BASE_URL}/get_order_details", params={"order_id": order_id})
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, default=str)}")
    return response.status_code == 200

def test_update_order_status(order_id):
    """Test updating order status"""
    print(f"\n🔍 Testing order status update for {order_id}...")
    
    update_data = {
        "order_id": order_id,
        "status": "confirmed"
    }
    
    response = requests.put(f"{BASE_URL}/update_order_status", json=update_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.status_code == 200

def main():
    """Run all tests"""
    print("🚀 Starting OMS API Tests")
    print("=" * 50)
    
    # Test health check
    if not test_health_check():
        print("❌ Health check failed!")
        return
    
    # Test order creation
    order_id = test_create_order()
    if not order_id:
        print("❌ Order creation failed!")
        return
    
    print(f"✅ Order created successfully: {order_id}")
    
    # Test get order details
    if not test_get_order_details(order_id):
        print("❌ Get order details failed!")
        return
    
    print("✅ Get order details successful")
    
    # Test update order status
    if not test_update_order_status(order_id):
        print("❌ Update order status failed!")
        return
    
    print("✅ Update order status successful")
    
    # Get order details again to verify update
    print(f"\n🔍 Verifying status update...")
    test_get_order_details(order_id)
    
    print("\n🎉 All tests completed successfully!")

if __name__ == "__main__":
    main()
