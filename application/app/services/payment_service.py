"""
Payment service for handling all payment-related operations.

This service manages payment records in the payment_details table,
keeping payment status completely separate from order status.
"""

import logging
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session

from app.models.payments import PaymentDetails
from app.models.orders import Order
from app.connections.database import get_db
from app.core.constants import PaymentStatus

logger = logging.getLogger(__name__)


class PaymentService:
    """Service for managing payment operations"""
    
    def __init__(self, db: Session = None):
        self.db = db or next(get_db())
    
    async def create_or_update_payment_record(
        self,
        order_id: int,
        payment_id: str,
        payment_amount: Decimal,
        payment_mode: str,
        cash_amount: Decimal = Decimal('0.00'),
        online_amount: Decimal = Decimal('0.00'),
        total_amount: Decimal = None,
        payment_status: int = PaymentStatus.PENDING
    ) -> Dict[str, Any]:
        """
        Create or update a payment record in the payments table.
        Uses upsert pattern: update if payment_id exists, create if not.
        
        Args:
            order_id: ID of the order this payment is for
            payment_id: External payment ID (e.g., Razorpay payment ID)
            payment_amount: Amount being paid
            payment_mode: Payment mode ('cash', 'online', 'cash_and_online')
            cash_amount: Cash portion of payment
            online_amount: Online portion of payment
            total_amount: Total amount (defaults to payment_amount)
        
        Returns:
            Dict with success status and payment details
        """
        try:
            # Set total_amount to payment_amount if not provided
            if total_amount is None:
                total_amount = payment_amount
            
            # Check if payment record already exists
            existing_payment = self.db.query(PaymentDetails).filter(
                PaymentDetails.payment_id == payment_id
            ).first()
            
            if existing_payment:
                # Payment details are immutable once created
                # Only update mutable fields like payment_status
                
                # Validate that immutable fields haven't changed
                if (existing_payment.order_id != order_id or 
                    existing_payment.payment_amount != payment_amount or
                    existing_payment.payment_mode != payment_mode or
                    existing_payment.cash_amount != cash_amount or
                    existing_payment.online_amount != online_amount or
                    existing_payment.total_amount != total_amount):
                    
                    logger.warning(f"Attempt to modify immutable payment details for payment_id {payment_id}. "
                                 f"Original: order_id={existing_payment.order_id}, amount={existing_payment.payment_amount}, "
                                 f"mode={existing_payment.payment_mode}. "
                                 f"Attempted: order_id={order_id}, amount={payment_amount}, mode={payment_mode}")
                
                # Only update the timestamp (status updates happen via separate method)
                existing_payment.updated_at = datetime.now()
                existing_payment.payment_status = payment_status
                self.db.commit()
                self.db.refresh(existing_payment)
                
                logger.info(f"Payment record {existing_payment.id} already exists for payment_id {payment_id} - no changes needed")
                payment_record = existing_payment
                action = "found_existing"
            else:
                # Create new payment record
                payment_record = PaymentDetails(
                    order_id=order_id,
                    payment_id=payment_id,
                    payment_amount=payment_amount,
                    payment_date=datetime.now(),
                    payment_mode=payment_mode,
                    cash_amount=cash_amount,
                    online_amount=online_amount,
                    total_amount=total_amount,
                    payment_status=payment_status
                )
                
                self.db.add(payment_record)
                self.db.commit()
                self.db.refresh(payment_record)
                
                logger.info(f"Created new payment record {payment_record.id} for payment_id {payment_id}")
                action = "created"
            
            return {
                "success": True,
                "action": action,
                "payment_record_id": payment_record.id,
                "payment_id": payment_id,
                "order_id": order_id,
                "amount": float(payment_amount),
                "status": payment_record.payment_status,
                "created_at": payment_record.created_at.isoformat() if payment_record.created_at else None
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating payment record: {str(e)}")
            return {
                "success": False,
                "message": f"Error creating payment record: {str(e)}"
            }
    
    async def update_payment_status(
        self,
        payment_id: str,
        new_status: int
    ) -> Dict[str, Any]:
        """
        Update only the payment status for an existing payment record.
        This is the correct way to update payment status from webhooks.
        Payment details (amount, mode, etc.) are immutable once created.
        
        Args:
            payment_id: External payment ID (e.g., Razorpay payment ID)
            new_status: New payment status code
        
        Returns:
            Dict with success status and updated payment details
        """
        try:
            # Find existing payment record
            payment_record = self.db.query(PaymentDetails).filter(
                PaymentDetails.payment_id == payment_id
            ).first()
            
            if not payment_record:
                logger.warning(f"Payment record not found for payment_id {payment_id}")
                return {
                    "success": False,
                    "message": f"Payment record not found for payment_id {payment_id}"
                }
            
            # Update only mutable fields
            old_status = payment_record.payment_status
            payment_record.payment_status = new_status
            payment_record.updated_at = datetime.now()
            
            self.db.commit()
            self.db.refresh(payment_record)
            
            logger.info(f"Updated payment status for payment_id {payment_id}: {old_status} → {new_status}")
            
            return {
                "success": True,
                "payment_record_id": payment_record.id,
                "payment_id": payment_id,
                "old_status": old_status,
                "new_status": new_status,
                "status_display": PaymentStatus.get_description(new_status),
                "updated_at": payment_record.updated_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error updating payment status: {str(e)}")
            return {
                "success": False,
                "message": f"Error updating payment status: {str(e)}"
            }
    
    async def get_payment_by_id(self, payment_id: str) -> Optional[PaymentDetails]:
        """Get payment record by external payment ID"""
        try:
            return self.db.query(PaymentDetails).filter(
                PaymentDetails.payment_id == payment_id
            ).first()
        except Exception as e:
            logger.error(f"Error fetching payment {payment_id}: {e}")
            return None
    
    async def get_payments_for_order(self, order_id: int) -> List[PaymentDetails]:
        """Get all payment records for an order"""
        try:
            return self.db.query(PaymentDetails).filter(
                PaymentDetails.order_id == order_id
            ).order_by(PaymentDetails.created_at.desc()).all()
        except Exception as e:
            logger.error(f"Error fetching payments for order {order_id}: {e}")
            return []
    
    async def get_payment_status_for_order(self, order_id: int) -> Dict[str, Any]:
        """
        Get payment status summary for an order.
        
        Returns:
            Dict with payment status information for the order
        """
        try:
            payments = await self.get_payments_for_order(order_id)
            
            if not payments:
                return {
                    "order_id": order_id,
                    "has_payments": False,
                    "payment_status": None,
                    "total_paid": 0.0,
                    "payment_count": 0
                }
            
            # Calculate totals and determine overall status
            total_paid = sum(
                float(p.payment_amount) for p in payments 
                if p.payment_status == PaymentStatus.COMPLETED
            )
            
            # Categorize payments by status
            completed_payments = [p for p in payments if p.payment_status == PaymentStatus.COMPLETED]
            pending_payments = [p for p in payments if p.payment_status == PaymentStatus.PENDING]
            failed_payments = [p for p in payments if p.payment_status == PaymentStatus.FAILED]
            refunded_payments = [p for p in payments if p.payment_status == PaymentStatus.REFUNDED]
            
            # Determine overall payment status with proper priority:
            # 1. If ANY payment is completed -> Overall status is COMPLETED
            # 2. If no completed but has pending -> Overall status is PENDING  
            # 3. If no completed/pending but has refunded -> Overall status is REFUNDED
            # 4. Only if ALL payments failed -> Overall status is FAILED
            if completed_payments:
                overall_status = PaymentStatus.COMPLETED
                logger.info(f"Order {order_id}: Overall status COMPLETED - {len(completed_payments)} completed payments found")
            elif pending_payments:
                overall_status = PaymentStatus.PENDING
                logger.info(f"Order {order_id}: Overall status PENDING - {len(pending_payments)} pending payments")
            elif refunded_payments:
                overall_status = PaymentStatus.REFUNDED
                logger.info(f"Order {order_id}: Overall status REFUNDED - {len(refunded_payments)} refunded payments")
            else:
                overall_status = PaymentStatus.FAILED
                logger.info(f"Order {order_id}: Overall status FAILED - only failed payments exist")

            return {
                "order_id": order_id,
                "has_payments": True,
                "payment_status_display": PaymentStatus.get_description(overall_status),
                "total_paid": total_paid,
                "payment_count": len(payments),
                "completed_count": len(completed_payments),
                "pending_count": len(pending_payments),
                "failed_count": len(failed_payments),
                "refunded_count": len(refunded_payments),
                "payments": [
                    {
                        "payment_id": payment.payment_id,
                        "payment_status_display": PaymentStatus.get_description(payment.payment_status),
                        "payment_amount": float(payment.payment_amount),
                        "payment_mode": payment.payment_mode,
                        "cash_amount": float(payment.cash_amount),
                        "online_amount": float(payment.online_amount),
                        "total_amount": float(payment.total_amount),
                        "created_at": payment.created_at.isoformat() if payment.created_at else None,
                        "updated_at": payment.updated_at.isoformat() if payment.updated_at else None
                    }
                    for payment in payments
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting payment status for order {order_id}: {e}")
            return {
                "order_id": order_id,
                "has_payments": False,
                "payment_status": None,
                "total_paid": 0.0,
                "payment_count": 0,
                "error": str(e)
            }


# Global payment service instance
payment_service = PaymentService()
