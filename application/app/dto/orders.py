from pydantic import BaseModel, Field, field_validator
from typing import List, Dict, Optional
from datetime import datetime

class OrderItemCreate(BaseModel):
    sku: str = Field(..., min_length=1, max_length=100)
    quantity: int = Field(..., gt=0)
    unit_price: float = Field(..., gt=0)
    sale_price: float = Field(..., gt=0)


class PaymentInfo(BaseModel):
    payment_mode: str = Field(..., description="Payment mode: 'cash', 'razorpay', 'cod', 'cash_and_online'")
    create_payment_order: bool = Field(False, description="Create payment order for online payments")

    @field_validator("payment_mode")
    def validate_payment_mode(cls, v):
        allowed = {"cash", "razorpay", "cod", "cash_and_online"}
        if v.lower() not in allowed:
            raise ValueError("payment_mode must be one of: 'cash', 'razorpay', 'cod', 'cash_and_online'")
        return v.lower()


class OrderAddress(BaseModel):
    full_name: str
    phone_number: str
    address_line1: str
    address_line2: Optional[str] = None
    city: str
    state: str
    postal_code: str
    country: str = "india"
    type_of_address: str


    @field_validator("type_of_address")
    def validate_type(cls, v):
        allowed = {"work", "home", "other"}
        if v not in allowed:
            raise ValueError("type_of_address must be one of: 'work', 'home', 'other'")
        return v

    @field_validator("phone_number")
    def validate_phone(cls, v):
        import re
        pattern = r"^(\+\d{1,2}\s?)?1?\-?\s?\(?\d{3}\)?[\s\-]?\d{3}[\s\-]?\d{4}$"
        if not re.fullmatch(pattern, v):
            raise ValueError("Invalid phone number format")
        return v

class OrderCreate(BaseModel):
    customer_id: str = Field(..., min_length=1, max_length=50)
    customer_name: str = Field(..., min_length=1, max_length=100)
    facility_id: str = Field(..., min_length=1, max_length=50)
    facility_name: str = Field(..., min_length=1, max_length=100)
    status: str = Field("pending", pattern="^(pending|confirmed|delivered|cancelled)$")
    total_amount: float = Field(..., gt=0)
    is_approved: bool = Field(True)
    items: List[OrderItemCreate]
    address: OrderAddress
    payment: PaymentInfo = Field(..., description="Payment information including payment mode")
    
    # Payment integration fields (backward compatibility)
    customer_email: Optional[str] = Field(None, description="Customer email for payment order")
    customer_phone: Optional[str] = Field(None, description="Customer phone for payment order")

class OrderStatusUpdate(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50)
    status: str = Field(..., pattern="^(pending|confirmed|delivered|cancelled)$")

class OrderItemStatusUpdate(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50)
    sku: str = Field(..., min_length=1, max_length=100)
    status: str = Field(..., pattern="^(pending|confirmed|delivered|cancelled|refunded)$")

class OrderCancelRequest(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50, description="Order ID to cancel")

class OrderCancelResponse(BaseModel):
    success: bool
    message: str
    order_id: str
    status: int
    wms_status: Optional[str] = None

class OrderResponse(BaseModel):
    success: bool
    message: str
    order_id: str
    eta: Optional[datetime] = None
    
    # Payment order details (when auto_create_payment_order=True)
    payment_order_details: Optional[Dict] = Field(None, description="Razorpay payment order details")
