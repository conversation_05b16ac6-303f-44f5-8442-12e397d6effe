import json
import httpx
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import asyncio
from dataclasses import dataclass
from app.integrations.wms_config import get_wms_config
from app.core.constants import OrderStatus
from app.connections.database import execute_raw_sql

logger = logging.getLogger(__name__)

@dataclass
class WMSToken:
    """WMS Token data structure"""
    access_token: str
    expires_at: datetime
    
    @property
    def is_expired(self) -> bool:
        """Check if token is expired with configurable buffer"""
        buffer_time = timedelta(minutes=get_wms_config().token_buffer_minutes)
        return datetime.now() >= (self.expires_at - buffer_time)

class WMSTokenManager:
    """Manages WMS authentication tokens with automatic refresh"""
    
    def __init__(self):
        self.config = get_wms_config()
        self._token: Optional[WMSToken] = None
        self._lock = asyncio.Lock()
    
    async def get_valid_token(self) -> str:
        """Get a valid token, refreshing if necessary"""
        async with self._lock:
            if self._token is None or self._token.is_expired:
                await self._refresh_token()
            return self._token.access_token
    
    async def _refresh_token(self) -> None:
        """Refresh the authentication token"""
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                # Assuming token endpoint - adjust based on actual ShipsyWMS API
                logger.info(f"Refreshing WMS token for client ID: {self.config.client_id}")
                logger.info(f"WMS base URL: {self.config.base_url}")
                logger.info(f"WMS client secret: {self.config.client_secret}")
                response = await client.post(
                    f"{self.config.base_url}/o/token/",
                    data={
                        "client_id": self.config.client_id,
                        "client_secret": self.config.client_secret,
                        "grant_type": "client_credentials"
                    }
                )
                
                if response.status_code == 200:
                    token_data = response.json()
                    expires_in = token_data.get("expires_in", 36000)  # 10 hours default
                    
                    self._token = WMSToken(
                        access_token=token_data["access_token"],
                        expires_at=datetime.now() + timedelta(seconds=expires_in)
                    )
                    logger.info("WMS token refreshed successfully")
                else:
                    logger.error(f"Failed to refresh WMS token: {response.status_code} - {response.text}")
                    raise Exception(f"Token refresh failed: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Error refreshing WMS token: {str(e)}")
            raise

class WMSService:
    """Service for integrating with ShipsyWMS"""
    
    def __init__(self):
        self.token_manager = WMSTokenManager()
        self.config = get_wms_config()
    
    def _format_datetime_for_wms(self, dt_value) -> str:
        """Format datetime for WMS API - handles both datetime objects and ISO strings"""
        if dt_value is None:
            return ""
        
        if isinstance(dt_value, str):
            # If it's already a string (ISO format), convert to datetime first
            try:
                from datetime import datetime
                dt_obj = datetime.fromisoformat(dt_value.replace('Z', '+00:00'))
                return dt_obj.strftime("%Y-%m-%d %H:%M:%S")
            except (ValueError, AttributeError):
                # If parsing fails, return the string as-is
                return dt_value
        elif hasattr(dt_value, 'strftime'):
            # If it's a datetime object
            return dt_value.strftime("%Y-%m-%d %H:%M:%S")
        else:
            # Fallback to string representation
            return str(dt_value)
    
    async def sync_order_by_id(self, facility_name: str, order_id: str, order_service) -> Dict[str, Any]:
        """Sync order to WMS by fetching data from database using facility_name and order_id"""
        try:
            # Fetch complete order data from database
            order_data = await self._fetch_order_data_from_db(order_id)
            if not order_data:
                logger.error(f"Order {order_id} not found in database")
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
                return {
                    "success": False,
                    "status": OrderStatus.WMS_SYNC_FAILED,
                    "message": "Order not found in database"
                }
            
            # Use the existing sync logic
            return await self.sync_order_to_wms(order_data, order_service)
            
        except Exception as e:
            logger.error(f"Error syncing order {order_id} by ID: {str(e)}")
            await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
            return {
                "success": False,
                "status": OrderStatus.WMS_SYNC_FAILED,
                "error": str(e),
                "message": "Exception occurred while syncing to WMS"
            }

    async def _fetch_order_data_from_db(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Fetch complete order data from database including items and address"""
        try:
            # Fetch order details
            order_query = """
                SELECT o.order_id, o.customer_id, o.customer_name, o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta, o.created_at
                FROM orders o
                WHERE o.order_id = :order_id
            """

            order_result = execute_raw_sql(order_query, {"order_id": order_id})
            if not order_result:
                return None

            order_row = order_result[0]

            # Fetch order items
            items_query = """
                SELECT oi.sku, oi.quantity, oi.unit_price, oi.sale_price, oi.status
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE o.order_id = :order_id
            """

            items_result = execute_raw_sql(items_query, {"order_id": order_id})
            items = []
            for item_row in items_result:
                items.append({
                    "sku": item_row["sku"],
                    "quantity": item_row["quantity"],
                    "unit_price": float(item_row["unit_price"]),
                    "sale_price": float(item_row["sale_price"])
                })

            # Fetch order payment
            payment_query = """
                SELECT pd.payment_id, pd.payment_date, pd.payment_amount, pd.payment_mode
                FROM payment_details pd
                JOIN orders o ON pd.order_id = o.id
                WHERE o.order_id = :order_id
                ORDER BY pd.created_at DESC
                LIMIT 1
            """

            payment_result = execute_raw_sql(payment_query, {"order_id": order_id})
            payment = {}
            if payment_result:
                payment_row = payment_result[0]
                payment.update({
                    "payment_id": payment_row["payment_id"],
                    "payment_date": payment_row["payment_date"].isoformat() if payment_row["payment_date"] else None,
                    "payment_amount": float(payment_row["payment_amount"]),
                    "payment_mode": payment_row["payment_mode"]
                })

            # Fetch order address
            address_query = """
                SELECT oa.full_name, oa.phone_number, oa.address_line1, oa.address_line2,
                       oa.city, oa.state, oa.postal_code, oa.country, oa.type_of_address
                FROM order_addresses oa
                JOIN orders o ON oa.order_id = o.id
                WHERE o.order_id = :order_id
            """

            address_result = execute_raw_sql(address_query, {"order_id": order_id})
            address = {}
            if address_result:
                addr_row = address_result[0]
                address = {
                    "full_name": addr_row["full_name"],
                    "phone_number": addr_row["phone_number"],
                    "address_line1": addr_row["address_line1"],
                    "address_line2": addr_row["address_line2"],
                    "city": addr_row["city"],
                    "state": addr_row["state"],
                    "postal_code": addr_row["postal_code"],
                    "country": addr_row["country"],
                    "type_of_address": addr_row["type_of_address"]
                }

            # Construct complete order data
            order_data = {
                "order_id": order_row["order_id"],
                "customer_id": order_row["customer_id"],
                "customer_name": order_row["customer_name"],
                "facility_id": order_row["facility_id"],
                "facility_name": order_row["facility_name"],
                "status": order_row["status"],
                "total_amount": float(order_row["total_amount"]),
                "created_at": order_row["created_at"].isoformat() if order_row["created_at"] else None,
                "items": items,
                "address": address,
                "payment": payment,
                "eta": order_row["eta"].isoformat() if order_row["eta"] else None
            }

            logger.info("Fetched order data from DB for order %s", json.dumps(order_data))
            
            return order_data
            
        except Exception as e:
            logger.error(f"Error fetching order data from DB for order {order_id}: {str(e)}")
            return None

    async def sync_order_to_wms(self, order_data: Dict[str, Any], order_service) -> Dict[str, Any]:
        """Sync order to WMS and update order status accordingly"""
        order_id = order_data.get('order_id')
        
        # Check if WMS integration is enabled
        if not self.config.integration_enabled:
            logger.info(f"WMS integration disabled - skipping sync for order {order_id}")
            return {
                "success": True,
                "status": OrderStatus.OPEN,
                "message": "WMS integration disabled - order remains in OMS only",
                "skipped": True
            }

        try:
            # Create order in WMS
            warehouse = order_data.get('facility_name')
            wms_result = await self.create_outbound_order(order_data, warehouse)

            if wms_result['success']:
                # Update order status to WMS_SYNCED (21)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNCED)
                logger.info(f"Order {order_id} synced to WMS successfully")
                return {
                    "success": True,
                    "status": OrderStatus.WMS_SYNCED,
                    "wms_order_id": wms_result.get('wms_order_id'),
                    "message": "Order synced to WMS successfully"
                }
            else:
                # Update order status to WMS_SYNC_FAILED (22)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
                logger.error(f"Order {order_id} failed to sync to WMS: {wms_result.get('message')}")
                return {
                    "success": False,
                    "status": OrderStatus.WMS_SYNC_FAILED,
                    "error": wms_result.get('error'),
                    "message": f"Failed to sync order to WMS: {wms_result.get('message')}"
                }

        except Exception as e:
            # Update order status to WMS_SYNC_FAILED (22) on exception
            await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
            logger.error(f"Exception while syncing order {order_id} to WMS: {str(e)}")
            return {
                "success": False,
                "status": OrderStatus.WMS_SYNC_FAILED,
                "error": str(e),
                "message": "Exception occurred while syncing to WMS"
            }

    async def create_outbound_order(self, order_data: Dict[str, Any], warehouse: str) -> Dict[str, Any]:
        """Create an outbound order in WMS"""
        # Check if WMS integration is enabled
        if not self.config.integration_enabled:
            logger.info(f"WMS integration disabled - skipping outbound order creation for order {order_data.get('order_id')}")
            return {
                "success": False,
                "message": "WMS integration disabled",
                "skipped": True
            }

        try:
            token = await self.token_manager.get_valid_token()
            wms_payload = self._transform_order_to_wms_format(order_data)
            wms_payload_data = json.dumps(wms_payload)

            headers = {
                "Authorization": token,
                "warehouse": warehouse,
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.post(
                    f"{self.config.base_url}/api/v1/outbound/orders/",
                    headers=headers,
                    data=wms_payload_data
                )

                if response.status_code in [200, 201]:
                    result = response.json()
                    logger.info(f"WMS order created successfully: {order_data.get('order_id')}")
                    logger.info(f"WMS order response: {result}")
                    return {
                        "success": True,
                        "wms_order_id": result.get("order_id"),
                        "message": "Order created in WMS successfully"
                    }
                else:
                    logger.error(f"WMS order creation failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"WMS API error: {response.status_code}",
                        "message": response.text
                    }
                    
        except Exception as e:
            logger.error(f"Error creating WMS order: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create order in WMS"
            }

    async def cancel_outbound_order(self, order_reference: str, warehouse: str) -> Dict[str, Any]:
        """Cancel an outbound order in WMS"""
        # Check if WMS integration is enabled
        if not self.config.integration_enabled:
            logger.info(f"WMS integration disabled - skipping order cancellation for {order_reference}")
            return {
                "success": False,
                "message": "WMS integration disabled",
                "skipped": True
            }

        try:
            token = await self.token_manager.get_valid_token()
            
            headers = {
                "Authorization": token,
                "warehouse": warehouse,
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=60.0) as client:
                # Assuming cancel endpoint - adjust based on actual API
                response = await client.post(
                    f"{self.config.base_url}/api/v1/outbound/orders/{order_reference}/cancel",
                    headers=headers
                )

                if response.status_code in [200, 201]:
                    logger.info(f"WMS order cancelled successfully: {order_reference}")
                    return {
                        "success": True,
                        "message": "Order cancelled in WMS successfully"
                    }
                else:
                    logger.error(f"WMS order cancellation failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"WMS API error: {response.status_code}",
                        "message": response.text
                    }

        except Exception as e:
            logger.error(f"Error cancelling WMS order: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to cancel order in WMS"
            }

    def _transform_order_to_wms_format(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform OMS order data to WMS format"""

        # Extract address information
        address = order_data.get("address", {})

        # Calculate promised time (24 hours from now as default)
        from datetime import datetime, timedelta
        promised_time = datetime.now() + timedelta(hours=24)
        slot_from = datetime.now() + timedelta(hours=23)
        slot_to = promised_time

        # Transform items
        wms_items = []
        for item in order_data.get("items", []):
            wms_items.append({
                "sku": item.get("sku"),
                "quantity": item.get("quantity"),
                "mrp": item.get("unit_price", item.get("sale_price", 0)),
                "sale_price": item.get("sale_price"),
                "sale_price_gst": item.get("sale_price"),
            })

        payment = order_data.get("payment", {})

        # Calculate charges (can be customized based on business logic)
        delivery_charge = 40  # Default delivery charge
        handling_charge = 4   # Default handling charge

        wms_payload = {
            "warehouse": order_data.get("facility_name", "HANOI"),
            "order_type": "Express",  # Default order type
            "order_reference": order_data.get("order_id"),
            "promised_time": promised_time.strftime("%Y-%m-%d %H:%M:%S"),
            "slot_from": slot_from.strftime("%Y-%m-%d %H:%M:%S"),
            "slot_to": slot_to.strftime("%Y-%m-%d %H:%M:%S"),
            "customer": {
                "customer_name": address.get("full_name", order_data.get("customer_name")),
                "customer_reference": order_data.get("customer_id"),
                "phone_number": address.get("phone_number"),
                "address_line_1": address.get("address_line1"),
                "address_line_2": address.get("address_line2", ""),
                "lat": "0.0",  # Default coordinates - can be enhanced with geocoding
                "long": "0.0",
                "city": address.get("city"),
                "state": address.get("state"),
                "pincode": address.get("postal_code")
            },
            "payment_info": {
                "payment_mode": payment.get("payment_mode", ""),
                "transaction_id": payment.get("payment_id", ""),
                "payment_date": self._format_datetime_for_wms(payment.get("payment_date")),
                "paid_amount": payment.get("payment_amount", 0),
                "method": payment.get("payment_mode", "")
            },
            "charges": [
                {
                    "name": "HANDLING_CHARGE",
                    "amount": str(handling_charge),
                    "tax_amount": "0"
                },
                {
                    "name": "DELIVERY_CHARGE",
                    "amount": str(delivery_charge),
                    "tax_amount": "0"
                }
            ],
            "items": wms_items
        }

        logger.info(f"WMS payload for order: {wms_payload}")

        return wms_payload

# Singleton instance
wms_service = WMSService()
