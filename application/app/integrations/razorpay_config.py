"""
Razorpay configuration management for the OMS system.

This module handles Razorpay API configuration including credentials,
endpoints, and integration settings.
"""

import os
from typing import Optional
from pydantic import BaseModel

RAZORPAY_INTEGRATION_ENABLED = os.getenv("RAZORPAY_INTEGRATION_ENABLED", "false").lower() == "true"
RAZORPAY_KEY_ID = os.getenv("RAZORPAY_KEY_ID")
RAZORPAY_KEY_SECRET = os.getenv("RAZORPAY_KEY_SECRET")
RAZORPAY_WEBHOOK_SECRET = os.getenv("RAZORPAY_WEBHOOK_SECRET")
RAZORPAY_BASE_URL = os.getenv("RAZORPAY_BASE_URL", "https://api.razorpay.com/v1")
RAZORPAY_CURRENCY = os.getenv("RAZORPAY_CURRENCY", "INR")
RAZORPAY_TIMEOUT = int(os.getenv("RAZORPAY_TIMEOUT", "30"))


class RazorpayConfig(BaseModel):
    """Razorpay configuration model"""
    
    key_id: str
    key_secret: str
    webhook_secret: str
    base_url: str
    integration_enabled: bool
    currency: str
    timeout: int
    
    @classmethod
    def from_environment(cls) -> "RazorpayConfig":
        """Create config from environment variables"""
        
        # Check if integration is enabled
        integration_enabled = RAZORPAY_INTEGRATION_ENABLED
        
        if not integration_enabled:
            # Return minimal config when disabled
            return cls(
                key_id="disabled",
                key_secret="disabled", 
                webhook_secret="disabled",
                integration_enabled=False,
                base_url="disabled",
                currency="disabled",
                timeout=0
            )
        
        # Get required environment variables
        key_id = RAZORPAY_KEY_ID
        key_secret = RAZORPAY_KEY_SECRET
        webhook_secret = RAZORPAY_WEBHOOK_SECRET
        
        if not all([key_id, key_secret, webhook_secret]):
            missing = []
            if not key_id:
                missing.append("RAZORPAY_KEY_ID")
            if not key_secret:
                missing.append("RAZORPAY_KEY_SECRET")
            if not webhook_secret:
                missing.append("RAZORPAY_WEBHOOK_SECRET")
            
            raise ValueError(f"Missing required Razorpay environment variables: {', '.join(missing)}")
        
        return cls(
            key_id=key_id,
            key_secret=key_secret,
            webhook_secret=webhook_secret,
            integration_enabled=integration_enabled,
            base_url=RAZORPAY_BASE_URL,
            currency=RAZORPAY_CURRENCY,
            timeout=RAZORPAY_TIMEOUT,
            
        )


# Global config instance
razorpay_config = RazorpayConfig.from_environment()
