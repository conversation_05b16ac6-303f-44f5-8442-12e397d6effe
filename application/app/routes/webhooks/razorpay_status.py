"""
Razorpay webhook routes for payment status updates.

This module handles webhook notifications from Razorpay payment gateway
for updating payment status in the OMS system.
"""

import json
import logging
from fastapi import APIRouter, Request, BackgroundTasks, HTTPException, Header
from fastapi.responses import JSONResponse

from app.integrations.razorpay_service import razorpay_service
from app.core.order_functions import create_payment_for_order, update_payment_status
from app.core.constants import PaymentStatus

# Set up logging
logger = logging.getLogger(__name__)

# Create router for webhook endpoints
webhook_router = APIRouter(prefix="", tags=["webhooks"])


@webhook_router.post("/razorpay_webhook")
async def razorpay_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    x_razorpay_signature: str = Header(None, alias="X-Razorpay-Signature")
):
    """
    Handle Razorpay webhooks for payment status updates.
    
    This endpoint receives webhook notifications from Razorpay
    and updates payment status accordingly.
    """
    try:
        # Get raw payload
        payload = await request.body()
        payload_str = payload.decode('utf-8')
        
        # Verify webhook signature
        if not x_razorpay_signature:
            logger.warning("Webhook received without signature")
            raise HTTPException(status_code=400, detail="Missing signature")
        
        is_verified = await razorpay_service.verify_webhook_signature(
            payload_str,
            x_razorpay_signature
        )
        
        if not is_verified:
            logger.warning("Invalid webhook signature")
            raise HTTPException(status_code=400, detail="Invalid signature")
            
        # Parse webhook payload
        webhook_data = json.loads(payload_str)
        event = webhook_data.get("event")
        entity = webhook_data.get("payload", {}).get("payment", {}).get("entity", {})
        
        logger.info(f"Received webhook event: {event}")
        
        # Handle payment events
        if event in ["payment.captured", "payment.failed", "payment.authorized"]:
            order_id = entity.get("notes", {}).get("oms_order_id")
            payment_id = entity.get("id")
            razorpay_status = entity.get("status")
            payment_amount = float(entity.get("amount", 0)) / 100  # Convert from paise
            
            if order_id and payment_id:
                # Map Razorpay status to our payment status
                if razorpay_status == "captured":
                    payment_status = PaymentStatus.COMPLETED
                elif razorpay_status == "failed":
                    payment_status = PaymentStatus.FAILED
                else:
                    payment_status = PaymentStatus.PENDING
                
                # Create payment record if it doesn't exist
                background_tasks.add_task(
                    create_payment_for_order,
                    order_id,
                    payment_id,
                    payment_amount,
                    "online"
                )
                
                # Update payment status in background (payments table only)
                background_tasks.add_task(
                    update_payment_status,
                    payment_id,
                    payment_status
                )
                
                logger.info(f"Webhook processed for order {order_id}, payment {payment_id}")
            else:
                logger.warning(f"Missing order_id or payment_id in webhook: {entity}")
        
        return JSONResponse(content={"status": "ok"})
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
