from fastapi import APIRouter, Request, Query, BackgroundTasks
from app.dto.orders import OrderCreate, OrderResponse  # type: ignore
from app.dto.orders import OrderStatusUpdate, OrderItemStatusUpdate  # type: ignore
from app.core.order_functions import (
    create_order_core,
    get_order_details_core,
    get_all_facility_orders_core,
)
from app.core.order_updates import (
    update_order_status_core,
    update_item_status_core,
)

pos_router = APIRouter(tags=["pos"])


@pos_router.post("/create_order", response_model=OrderResponse)
async def create_order(order: OrderCreate, request: Request, background_tasks: BackgroundTasks):
    """Create order via POS system."""
    return await create_order_core(order, request, background_tasks, "pos")


@pos_router.get("/order_details")
async def get_order_details(order_id: str = Query(..., description="Order ID")):
    """Retrieve order details via POS system."""
    return await get_order_details_core(order_id)


@pos_router.get("/orders")
async def get_all_orders(request: Request, facility_name: str = Query(..., description="Facility name"), limit: int = 20, offset: int = 0, sort_order: str = "desc"):
    """List orders for a logged-in POS user."""
    return await get_all_facility_orders_core(request, facility_name, limit, offset, sort_order)

# ---- Update endpoints ----
@pos_router.put("/update_order_status")
@pos_router.patch("/update_order_status")
async def update_order_status(order_update: OrderStatusUpdate):
    """Update order status (POS)."""
    return await update_order_status_core(order_update)


@pos_router.put("/update_item_status")
@pos_router.patch("/update_item_status")
async def update_item_status(item_update: OrderItemStatusUpdate):
    """Update item status (POS)."""
    return await update_item_status_core(item_update)
