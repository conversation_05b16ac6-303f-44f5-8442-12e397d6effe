import os
from hyperdx.opentelemetry import configure_opentelemetry

def init_hyperdx():
    """Initialize HyperDX monitoring"""
    api_key = os.getenv('HYPERDX_API_KEY')
    service_name = os.getenv('OTEL_SERVICE_NAME') or os.getenv('HYPERDX_SERVICE_NAME', 'rozana-oms-service')
    
    if not api_key:
        print("Warning: HYPERDX_API_KEY not set. HyperDX monitoring disabled.")
        return
    
    # Set required environment variables for OpenTelemetry
    os.environ['HYPERDX_API_KEY'] = api_key
    os.environ['OTEL_SERVICE_NAME'] = service_name
    
    # Enable advanced network capture if configured
    if os.getenv('HYPERDX_ENABLE_ADVANCED_NETWORK_CAPTURE'):
        os.environ['HYPERDX_ENABLE_ADVANCED_NETWORK_CAPTURE'] = '1'
    
    # Configure OpenTelemetry with HyperDX
    configure_opentelemetry()
    
    print(f"HyperDX initialized for service: {service_name}")

# For Gunicorn post-fork hook
def post_fork(server, worker):
    """Post-fork hook for Gunicorn to reinitialize OpenTelemetry in worker processes"""
    configure_opentelemetry()
