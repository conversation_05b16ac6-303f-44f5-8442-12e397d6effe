import multiprocessing
import os

# HyperDX post-fork hook
def post_fork(server, worker):
    """Post-fork hook for <PERSON><PERSON> to reinitialize OpenTelemetry in worker processes"""
    try:
        from hyperdx.opentelemetry import configure_opentelemetry
        configure_opentelemetry()
        print(f"HyperDX reinitialized in worker {worker.pid}")
    except ImportError:
        print("HyperDX not available in worker process")

# Server socket
bind = f"0.0.0.0:{os.getenv('PORT', '8000')}"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = 'uvicorn.workers.UvicornWorker'
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 100

# Logging
accesslog = '-'
errorlog = '-'
loglevel = 'info'
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = 'rozana-oms-api'
