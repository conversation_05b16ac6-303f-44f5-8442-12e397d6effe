"""create_orders_items_table

Revision ID: dd55a25d4835
Revises: 532e31def493
Create Date: 2025-07-26 07:15:17.186828

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dd55a25d4835'
down_revision: Union[str, Sequence[str], None] = '532e31def493'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('order_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('order_id', sa.Integer(), nullable=False),
        sa.Column('sku', sa.String(length=100), nullable=False),
        sa.Column('quantity', sa.Integer(), nullable=False),
        sa.Column('unit_price', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('sale_price', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('status', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text("timezone('Asia/Kolkata', current_timestamp)")),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text("timezone('Asia/Kolkata', current_timestamp)")),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_order_items_order_sku', 'order_items', ['order_id', 'sku'], unique=False)
    op.create_index('idx_order_items_order_status', 'order_items', ['order_id', 'status'], unique=False)
    op.create_index('idx_order_items_sku_status', 'order_items', ['sku', 'status'], unique=False)
    op.create_index(op.f('ix_order_items_id'), 'order_items', ['id'], unique=False)
    op.create_index(op.f('ix_order_items_order_id'), 'order_items', ['order_id'], unique=False)
    op.create_index(op.f('ix_order_items_sku'), 'order_items', ['sku'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_order_items_sku'), table_name='order_items')
    op.drop_index(op.f('ix_order_items_order_id'), table_name='order_items')
    op.drop_index(op.f('ix_order_items_id'), table_name='order_items')
    op.drop_index('idx_order_items_sku_status', table_name='order_items')
    op.drop_index('idx_order_items_order_status', table_name='order_items')
    op.drop_index('idx_order_items_order_sku', table_name='order_items')
    op.drop_table('order_items')
    # ### end Alembic commands ###
