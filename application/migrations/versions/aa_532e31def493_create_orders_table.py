"""create_orders_table

Revision ID: 532e31def493
Revises: 
Create Date: 2025-07-26 07:12:46.893916

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '532e31def493'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('orders',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('random_prefix', sa.String(length=4), nullable=False),
        sa.Column('order_id', sa.String(length=50), sa.Computed("random_prefix || id::TEXT", persisted=True), nullable=False),
        sa.Column('customer_id', sa.String(length=50), nullable=False),
        sa.Column('customer_name', sa.String(length=100), nullable=False),
        sa.Column('facility_id', sa.String(length=50), nullable=False),
        sa.Column('facility_name', sa.String(length=100), nullable=False),
        sa.Column('status', sa.Integer(), nullable=False),
        sa.Column('total_amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('eta', sa.TIMESTAMP(), nullable=True),
        sa.Column('order_mode', sa.String(length=20), nullable=False),
        sa.Column('is_approved', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text("timezone('Asia/Kolkata', current_timestamp)")),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text("timezone('Asia/Kolkata', current_timestamp)")),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_orders_customer_status', 'orders', ['customer_id', 'status'], unique=False)
    op.create_index('idx_orders_facility_status', 'orders', ['facility_id', 'status'], unique=False)
    op.create_index('idx_orders_status_created', 'orders', ['status', 'created_at'], unique=False)
    op.create_index(op.f('ix_orders_customer_id'), 'orders', ['customer_id'], unique=False)
    op.create_index(op.f('ix_orders_facility_id'), 'orders', ['facility_id'], unique=False)
    op.create_index(op.f('ix_orders_id'), 'orders', ['id'], unique=False)
    op.create_index(op.f('ix_orders_order_id'), 'orders', ['order_id'], unique=True)
    op.create_index(op.f('ix_orders_status'), 'orders', ['status'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_index(op.f('ix_orders_status'), table_name='orders')
    op.drop_index(op.f('ix_orders_order_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_facility_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_customer_id'), table_name='orders')
    op.drop_index('idx_orders_status_created', table_name='orders')
    op.drop_index('idx_orders_facility_status', table_name='orders')
    op.drop_index('idx_orders_customer_status', table_name='orders')
    op.drop_table('orders')
    # ### end Alembic commands ###
